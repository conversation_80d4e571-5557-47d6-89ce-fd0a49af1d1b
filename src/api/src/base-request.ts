import { message } from 'antd'
import { getCurrentLanguage, t } from '../../i18n/simple'
import Cookies from 'js-cookie';

/**
 * 未授权错误类
 */
export class UnauthorizedError extends Error {
	constructor(message: string) {
		super('Unauthorized')
		this.name = 'UnauthorizedError'
		this.message = message
	}
}

export class XRequest {
	constructor(options: { baseURL: string; medxyToken: string, appId?: string }) {
		this.options = options
	}

	options: {
		baseURL: string
		medxyToken: string
		appId?: string
	}

	/**
	 * 动态获取最新的medxyToken
	 * 优先使用Cookie中的最新token，如果没有则使用构造时传入的token
	 */
	private getCurrentToken(): string {
		const cookieToken = Cookies.get('medxyToken')
		return cookieToken || this.options.medxyToken
	}

	async baseRequest(url: string, options: RequestInit) {
		// 使用动态获取的最新token
		const currentToken = this.getCurrentToken()

		const result = await fetch(`${this.options.baseURL}${url}`, {
			...options,
			headers: {
				...options.headers,
				...(currentToken ? { Authorization: `Bearer ${currentToken}` } : {}),
			},
		})

		if (result.status === 401) {
			this.cleanCookies()

			message.error(t('errors.unauthorized'))
			throw new UnauthorizedError('Unauthorized')
		}
		return result
	}

	async jsonRequest(url: string, options: RequestInit) {
		const result = await this.baseRequest(url, {
			...options,
			headers: {
				...options.headers,
				'Content-Type': 'application/json',
			},
		})
		const res:any = await result.json()
		if (res.code !== 0) {
			if(res.code == 401){
				this.cleanCookies()

				message.error('未授权, 请重新登录')
				return res
			}
			message.error(res.msg)
		} else {
			return res.data
		}
	}

	async get(url: string, params?: Record<string, string>, headers: Record<string, string> = {}) {
		// 自动添加当前语言的locale参数
		const currentLang = getCurrentLanguage()
		const finalParams = { ...params, locale: currentLang }
		const queryString = `?${new URLSearchParams(finalParams).toString()}`
		const result = await this.jsonRequest(`${url}${queryString}`, {
			method: 'GET',
			headers,
		})
		return result
	}

	async post(url: string, params?: Record<string, unknown>, headers: Record<string, string> = {}) {
		// 自动添加当前语言的locale参数到URL
		const currentLang = getCurrentLanguage()
		const urlWithLocale = url.includes('?') ? `${url}&locale=${currentLang}` : `${url}?locale=${currentLang}`
		const result = await this.jsonRequest(urlWithLocale, {
			method: 'POST',
			body: JSON.stringify(params),
			headers,
		})
		return result
	}

	async delete(
		url: string,
		params?: Record<string, unknown>,
		headers: Record<string, string> = {},
	) {
		// 自动添加当前语言的locale参数到URL
		const currentLang = getCurrentLanguage()
		const urlWithLocale = url.includes('?') ? `${url}&locale=${currentLang}` : `${url}?locale=${currentLang}`
		const result = await this.jsonRequest(urlWithLocale, {
			method: 'DELETE',
			body: JSON.stringify(params),
			headers,
		})
		return result
	}

	async cleanCookies() {
		console.log("开始删除用户信息-cleanCookies");
		Cookies.remove("userInfo", { domain: ".medsci.cn" });
		Cookies.remove("userInfo" ,{ domain: ".medon.com.cn" });
		Cookies.remove("userInfo");
		Cookies.remove("yudaoToken" ,{ domain: ".medsci.cn" });
		Cookies.remove("yudaoToken",{ domain: ".medon.com.cn" });
		Cookies.remove("yudaoToken",{ domain: ".ai.medon.com.cn" });
		Cookies.remove("yudaoToken");
		Cookies.remove("medxyToken");
		localStorage.removeItem("conversation")
		localStorage.removeItem("hasuraToken");
		localStorage.removeItem("socialUserId");
		localStorage.removeItem("socialType");
		localStorage.removeItem("openid");
		console.log("结束删除用户信息-cleanCookies");
	}
}

export default XRequest
