import React, { useEffect, useMemo } from 'react'
import { Helmet } from 'react-helmet-async'
import { useSimpleTranslation } from '../i18n/simple-hooks'

export interface PageTitleProps {
  /** 页面类型 */
  pageType?: 'home' | 'chat' | 'newChat' | 'loading' | 'error' | 'notFound' | 'login' | 'signup'
  /** 应用名称键 */
  appKey?: string
  /** 自定义标题（优先级最高） */
  customTitle?: string
  /** 对话标题（用于对话页面） */
  chatTitle?: string
  /** 是否显示应用名称 */
  showAppName?: boolean
  /** 是否显示后缀 */
  showSuffix?: boolean
  /** 自定义分隔符 */
  separator?: string
  /** 最大标题长度（超出会截断） */
  maxLength?: number
}

/**
 * 页面标题组件
 * 使用react-helmet-async管理document.title
 * 支持国际化、动态更新、SEO优化
 */
const PageTitle: React.FC<PageTitleProps> = ({
  pageType = 'home',
  appKey,
  customTitle,
  chatTitle,
  showAppName = true,
  showSuffix = true,
  separator,
  maxLength = 60
}) => {
  const { t } = useSimpleTranslation()

  // 生成页面标题
  const title = useMemo(() => {
    // 如果有自定义标题，直接使用
    if (customTitle) {
      return truncateTitle(customTitle, maxLength)
    }

    // 获取翻译值
    const pageTitleSeparator = separator || t('pageTitle.separator')
    const suffix = t('pageTitle.suffix')
    
    // 获取应用名称
    const appName = appKey ? t(`pageTitle.apps.${appKey}`) : ''
    
    // 根据页面类型生成标题
    let titleParts: string[] = []
    
    switch (pageType) {
      case 'home':
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break
        
      case 'chat':
        if (chatTitle) {
          titleParts.push(chatTitle)
        } else {
          titleParts.push(t('pageTitle.chat'))
        }
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break
        
      case 'newChat':
        titleParts.push(t('pageTitle.newChat'))
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break
        
      case 'loading':
        titleParts.push(t('pageTitle.loading'))
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break
        
      case 'error':
        titleParts.push(t('pageTitle.error'))
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break
        
      case 'notFound':
        titleParts.push(t('pageTitle.notFound'))
        break
        
      case 'login':
        titleParts.push(t('pageTitle.login'))
        break
        
      case 'signup':
        titleParts.push(t('pageTitle.signup'))
        break

      default:
        if (showAppName && appName) {
          titleParts.push(appName)
        }
    }
    
    // 添加后缀
    if (showSuffix) {
      titleParts.push(suffix)
    }
    
    // 组合标题
    const finalTitle = titleParts.join(pageTitleSeparator)
    
    return truncateTitle(finalTitle, maxLength)
  }, [
    customTitle,
    pageType,
    appKey,
    chatTitle,
    showAppName,
    showSuffix,
    separator,
    maxLength,
    t
  ])

  // 截断标题函数
  function truncateTitle(title: string, maxLen: number): string {
    if (title.length <= maxLen) {
      return title
    }
    
    // 智能截断：尽量保留完整的词语
    const truncated = title.substring(0, maxLen - 3)
    const lastSpace = truncated.lastIndexOf(' ')
    const lastSeparator = truncated.lastIndexOf(' - ')
    
    // 如果找到分隔符，在分隔符处截断
    if (lastSeparator > 0 && lastSeparator > truncated.length * 0.6) {
      return truncated.substring(0, lastSeparator) + '...'
    }
    
    // 如果找到空格，在空格处截断
    if (lastSpace > 0 && lastSpace > truncated.length * 0.7) {
      return truncated.substring(0, lastSpace) + '...'
    }
    
    // 否则直接截断
    return truncated + '...'
  }

  // 调试日志
  useEffect(() => {
    if (import.meta.env.DEV) {
      console.log('[PageTitle] Title updated:', title)
    }
  }, [title])

  return (
    <Helmet>
      <title>{title}</title>
      {/* 添加一些SEO相关的meta标签 */}
      <meta property="og:title" content={title} />
      <meta name="twitter:title" content={title} />
    </Helmet>
  )
}

export default PageTitle
