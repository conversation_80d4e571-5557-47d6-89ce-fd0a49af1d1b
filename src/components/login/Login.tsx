import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useSimpleTranslation } from '../../i18n/simple-hooks';
import Cookies from 'js-cookie';
import { XAiApi } from '../../api/src/xai-api';
import { getEnvConfig } from '../../utils/envConfig';
import PageTitle from '../../components/PageTitle';
import { LoginEventManager } from '../../utils/loginEventManager';

/**
 * React版登录页面组件
 */
const Login: React.FC = () => {
  const { t } = useSimpleTranslation();
  const { lang, socialType } = useParams<{ lang: string; socialType: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const code = queryParams.get('authCode');
  const state = queryParams.get('authState');

  // 表单状态
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // 环境配置
  const envConfig = getEnvConfig();
  const apiBase = envConfig.apiBase;
  const xAiApi = new XAiApi({
    user: 'nologin',
    apiBase: apiBase,
    medxyToken: ''
  });

  console.log('novax-login-page');

  // 检查是否已登录，如果已登录则重定向
  useEffect(() => {
    const medxyToken = Cookies.get('medxyToken');
    const userInfoString = Cookies.get('userInfo');

    if (medxyToken && userInfoString) {
      // 已登录，重定向到首页或之前的页面
      const redirectUrl = window.sessionStorage.getItem('redirectUrl');
      if (redirectUrl) {
        window.sessionStorage.removeItem('redirectUrl');
        window.location.href = redirectUrl;
      } else {
        navigate(`/${lang || 'zh'}`);
      }
    }
  }, [lang, navigate]);

  // 处理社交登录回调
  useEffect(() => {
    if (!Cookies.get('medxyToken') && socialType && code && state) {
      // xAiApi.logout();
      setLoading(true);
      xAiApi.socialLogin(socialType, code, state).then(res => {
        console.log('socialLogin res:', res);
        if (res?.token && res?.htoken) {
          const hostname = window.location.hostname;
          localStorage.setItem('hasuraToken', res.htoken);
          localStorage.setItem('openid', res.openid || '');
          localStorage.setItem('socialUserId', res.socialUserId || '');
          localStorage.setItem('socialType', res.socialType || '');
          res.userInfo.userId = res.userInfo.openid
          res.userInfo.plaintextUserId = res.userInfo.socialUserId
          res.userInfo.token = {accessToken:res.userInfo.openid, accessTokenExpireTime: 630720000, refreshToken:res.userInfo.openid}
          if(hostname.includes(".medsci.cn")){
            Cookies.set('medxyToken', res.token);
            Cookies.set('userInfo', JSON.stringify(res.userInfo), { expires: 365 , domain:".medsci.cn"})
          }else if(hostname.includes(".medon.com.cn")){
            Cookies.set('medxyToken', res.token);
            Cookies.set('userInfo', JSON.stringify(res.userInfo), { expires: 365 , domain:".medon.com.cn"})
          }else if(hostname.includes("medxy.ai")){ 
            Cookies.set('medxyToken', res.token);
            Cookies.set('userInfo', JSON.stringify(res.userInfo), { expires: 365})
          } else{
            Cookies.set('medxyToken', res.token);
            Cookies.set('userInfo', JSON.stringify(res.userInfo), { expires: 365})
          }

          // 使用统一的事件管理器触发登录事件（防重复）
          LoginEventManager.triggerLoginEvents(res.userInfo, { immediate: true });

          const redirectUrl = window.sessionStorage.getItem('redirectUrl');
          if (redirectUrl) {
            window.sessionStorage.removeItem('redirectUrl');
            window.location.href = redirectUrl;
          } else {
            navigate(`/${lang || 'zh'}`);
          }
        } else {
          setError(t('login.login_failed'));
        }
      }).catch(err => {
        setError(err.message || t('login.login_failed'));
      }).finally(() => {
        setLoading(false);
      });
    }
  }, [socialType, code, state, lang, navigate, t, xAiApi]);

  // 处理社交登录
  const handleSocialLogin = (socialType: number) => {
    setLoading(true);
    setError('');
    
    // 保存当前URL用于登录后重定向
    window.sessionStorage.setItem('redirectUrl', window.location.href);
    
    // 调用社交登录重定向API
    xAiApi.socialAuthRedirect(socialType).then((res:any) => {
      if (res) {
        window.location.href = res;
      } else {
        setError(t('login.login_failed'));
        setLoading(false);
      }
    }).catch(err => {
      setError(err.message || t('login.login_failed'));
      setLoading(false);
    });
  };

  // 处理邮箱登录
  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      // 邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error(t('login.please_enter_valid_email'));
      }
      
      // 密码验证
      if (!password || password.length < 8) {
        throw new Error(t('login.password_cannot_be_empty'));
      }
      
      const fromPlatform = 'medxy'
      // 调用登录API
      const response = await xAiApi.login({
        fromPlatform,
        email,
        password
      });
      
      if (response?.token) {
        // 设置Cookie和localStorage
        const hostname = window.location.hostname;
        const isUp = hostname.includes('medon.com.cn') || hostname.includes('medsci.cn');
        
        // 设置Cookie
        Cookies.set("medxyToken", response.token);
        
        if (response.userInfo) {
          Cookies.set("userInfo", JSON.stringify(response.userInfo), { domain: isUp ? '.medsci.cn' : undefined });
        }
        
        // 设置localStorage
        localStorage.setItem("hasuraToken", response.htoken || '');
        localStorage.setItem("openid", response.openid || '');
        localStorage.setItem("socialUserId", response.socialUserId || '');
        localStorage.setItem("socialType", response.socialType || '');
        
        // 使用统一的事件管理器触发登录事件（防重复）
        LoginEventManager.triggerLoginEvents(response.userInfo, { immediate: true });
        
        // 登录成功后重定向
        const redirectUrl = window.sessionStorage.getItem('redirectUrl');
        if (redirectUrl) {
          window.sessionStorage.removeItem('redirectUrl');
          window.location.href = redirectUrl;
        } else {
          navigate(`/${lang || 'zh'}`);
        }
      } else {
        throw new Error(t('login.login_failed'));
      }
    } catch (err: any) {
      setError(err.message || t('login.login_failed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <PageTitle
        pageType="login"
        appKey={''}
      />

    <div className="bg-background text-foreground antialiased min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-md p-6 md:p-8 space-y-6 md:space-y-8 bg-white rounded-xl shadow-lg">
        <div className="text-center">
          <h1 className="text-2xl font-bold">{t('login.login_to_MedSci_xAI')}</h1>
          <p className="mt-2 text-gray-600">{t('login.welcome_back_please_login_to_continue')}</p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative flex items-center" role="alert">
            <svg className="h-5 w-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        <div className="space-y-4">
          {/* 社交登录按钮 */}
          <div className="space-y-2">
             <button 
              className="w-full flex items-center justify-center space-x-2 py-2 px-4 border border-gray-300 rounded-xl shadow-sm bg-white hover:bg-gray-50"
              onClick={() => handleSocialLogin(55)}
              disabled={loading}
            >
              <img 
                src="https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png" 
                alt="Google" 
                className="w-5 h-5"
              />
              <span>{t('login.continue_with_google')}</span>
            </button>

            <button 
              className="w-full flex items-center justify-center space-x-2 py-2 px-4 border border-gray-300 rounded-xl shadow-sm bg-white hover:bg-gray-50"
              onClick={() => handleSocialLogin(56)}
              disabled={loading}
            >
              <img 
                src="https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png" 
                alt="Facebook" 
                className="w-5 h-5"
              />
              <span>{t('login.continue_with_facebook')}</span>
            </button>
          </div>

          <div className="flex items-center">
            <div className="flex-grow border-t border-gray-300"></div>
            <span className="px-3 text-gray-500 bg-white">{t('login.or')}</span>
            <div className="flex-grow border-t border-gray-300"></div>
          </div>

          {/* 邮箱登录表单 */}
          <form className="space-y-4" onSubmit={handleEmailLogin}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                <span className="text-red-500">*</span>{t('login.email')}
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                disabled={loading}
              />
            </div>

            <div>
              <div className="flex justify-between items-center">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  <span className="text-red-500">*</span>{t('login.password')}
                </label>
                <a href={`/${lang}/forgot-password`} className="text-xs text-gray-600 hover:text-gray-900">
                  {t('login.forgot_password')}
                </a>
              </div>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  disabled={loading}
                />
                <div
                  className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                      <svg focusable="false" data-icon="eye" width="1em" height="1em" fill="currentColor" aria-hidden="true" viewBox="64 64 896 896"><path d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"></path></svg>
                    ) : (
                    <svg focusable="false" data-icon="eye-invisible" width="1em" height="1em" fill="currentColor" aria-hidden="true" viewBox="64 64 896 896"><path d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"></path><path d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"></path></svg>
                  )}
                </div>
              </div>
            </div>

            <div>
              <button
                  type="submit"
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t('login.logging_in')}
                    </>
                  ) : t('login.continue')}
                </button>
            </div>
          </form>
        </div>

        <div className="text-center mt-4">
          <span className="text-sm text-gray-600">{t('login.no_account_yet')}</span>
          <a href={`/${lang}/sign-up`} className="ml-1 text-sm text-gray-900 font-medium hover:text-gray-700">
            {t('login.signUp')}
          </a>
        </div>
      </div>
    </div>
    </>
  );
};

export default Login;
