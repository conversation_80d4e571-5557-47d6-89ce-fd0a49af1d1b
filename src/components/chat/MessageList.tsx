import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react'
import UserMessage from './UserMessage'
import AssistantMessage from './AssistantMessage'
import LoadingMessage from './LoadingMessage'
import {DifyApi} from '../../api/src/dify-api'

// 工作流节点数据
interface WorkflowNode {
  id: string
  type: string
  title: string
  status: 'running' | 'success' | 'error'
  parallel_id?: string
  created_at: number
  error?: string
  outputs?: any
  process_data?: any
  inputs?: any
  // 新增：节点分类字段
  displayCategory?: 'left' | 'right' | 'result' | 'none'
}

// 文件上传项类型定义
interface UploadFileItem {
  uid: string
  name: string
  status: 'uploading' | 'done' | 'error'
  size: number
  type: 'document' | 'image' | 'audio' | 'video'
  originFileObj?: { uid: string }
  percent: number
  transfer_method: 'local_file'
  upload_file_id?: string
  error?: string
}

interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  feedback?: 'like' | 'dislike' | null
  timestamp: Date
  thinking?: string
  steps?: string[]
  isGenerating?: boolean
  searchResults?: SearchResult[]
  browsingResults?: string[]
  // 新增：文件上传相关字段
  files?: UploadFileItem[]
  // 新增：评审相关字段
  isReviewing?: boolean
  reviewContent?: string
  // 新增：复审相关字段
  isSecondReviewing?: boolean
  secondReviewContent?: string
  // 新增：工作流相关字段
  workflowNodes?: WorkflowNode[]
  workflowStatus?: 'running' | 'finished'
  // 新增：分类显示的节点数据
  leftPanelNodes?: WorkflowNode[]    // 显示在左侧面板的节点
  rightPanelNodes?: WorkflowNode[]   // 显示在右侧面板的节点
  resultNodes?: WorkflowNode[]       // 显示在结果组件的节点
}

interface SearchResult {
  title: string
  type: string
  url: string
  description?: string
}

interface MessageListProps {
  messages: ChatMessage[]
  isLoading?: boolean
  difyApi: DifyApi
}

// 深度比较函数，用于比较消息对象的关键属性
const areMessagesEqual = (prevMessage: ChatMessage, nextMessage: ChatMessage): boolean => {
  // 比较基本属性
  if (
    prevMessage.id !== nextMessage.id ||
    prevMessage.content !== nextMessage.content ||
    prevMessage.thinking !== nextMessage.thinking ||
    prevMessage.isGenerating !== nextMessage.isGenerating ||
    prevMessage.workflowStatus !== nextMessage.workflowStatus ||
    prevMessage.isReviewing !== nextMessage.isReviewing ||
    prevMessage.reviewContent !== nextMessage.reviewContent ||
    prevMessage.isSecondReviewing !== nextMessage.isSecondReviewing ||
    prevMessage.secondReviewContent !== nextMessage.secondReviewContent
  ) {
    return false
  }

  // 比较数组长度
  const prevWorkflowNodesLength = prevMessage.workflowNodes?.length || 0
  const nextWorkflowNodesLength = nextMessage.workflowNodes?.length || 0
  const prevLeftPanelNodesLength = prevMessage.leftPanelNodes?.length || 0
  const nextLeftPanelNodesLength = nextMessage.leftPanelNodes?.length || 0
  const prevRightPanelNodesLength = prevMessage.rightPanelNodes?.length || 0
  const nextRightPanelNodesLength = nextMessage.rightPanelNodes?.length || 0
  const prevResultNodesLength = prevMessage.resultNodes?.length || 0
  const nextResultNodesLength = nextMessage.resultNodes?.length || 0

  if (
    prevWorkflowNodesLength !== nextWorkflowNodesLength ||
    prevLeftPanelNodesLength !== nextLeftPanelNodesLength ||
    prevRightPanelNodesLength !== nextRightPanelNodesLength ||
    prevResultNodesLength !== nextResultNodesLength
  ) {
    return false
  }

  // 比较节点内容（简化版，只比较关键字段）
  const compareNodes = (prevNodes: any[] = [], nextNodes: any[] = []) => {
    if (prevNodes.length !== nextNodes.length) return false

    for (let i = 0; i < prevNodes.length; i++) {
      const prevNode = prevNodes[i]
      const nextNode = nextNodes[i]

      if (
        prevNode.id !== nextNode.id ||
        prevNode.status !== nextNode.status ||
        prevNode.outputs?.answer !== nextNode.outputs?.answer
      ) {
        return false
      }
    }
    return true
  }

  return (
    compareNodes(prevMessage.workflowNodes, nextMessage.workflowNodes) &&
    compareNodes(prevMessage.leftPanelNodes, nextMessage.leftPanelNodes) &&
    compareNodes(prevMessage.rightPanelNodes, nextMessage.rightPanelNodes) &&
    compareNodes(prevMessage.resultNodes, nextMessage.resultNodes)
  )
}

// 优化的 AssistantMessage 渲染组件，使用深度比较避免不必要的重新渲染
const OptimizedAssistantMessage: React.FC<{ message: ChatMessage, difyApi: DifyApi }> = React.memo(({ message, difyApi }) => {
  // 使用 useMemo 缓存 props 对象，避免每次渲染都创建新对象
  const assistantMessageProps = useMemo(() => ({
    difyApi: difyApi,
    messageId: message.id,
    feedback: message.feedback,
    content: message.content,
    thinking: message.thinking,
    steps: message.steps,
    isGenerating: message.isGenerating,
    searchResults: message.searchResults,
    browsingResults: message.browsingResults,
    isReviewing: message.isReviewing,
    reviewContent: message.reviewContent,
    isSecondReviewing: message.isSecondReviewing,
    secondReviewContent: message.secondReviewContent,
    workflowNodes: message.workflowNodes,
    workflowStatus: message.workflowStatus,
    leftPanelNodes: message.leftPanelNodes,
    rightPanelNodes: message.rightPanelNodes,
    resultNodes: message.resultNodes
  }), [
    message.content, message.thinking, message.steps, message.isGenerating,
    message.searchResults, message.browsingResults, message.isReviewing,
    message.reviewContent, message.isSecondReviewing, message.secondReviewContent,
    message.workflowNodes, message.workflowStatus, message.leftPanelNodes,
    message.rightPanelNodes, message.resultNodes
  ])

  return <AssistantMessage {...assistantMessageProps} />
}, (prevProps, nextProps) => {
  const isEqual = areMessagesEqual(prevProps.message, nextProps.message)
  return isEqual
})

/**
 * 消息列表组件 - 管理和显示所有聊天消息
 * 包含智能自动滚动功能 - 只有在用户没有手动滚动时才自动滚动到底部
 */
const MessageList: React.FC<MessageListProps> = React.memo(({ messages, isLoading = false, difyApi }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isUserScrolling, setIsUserScrolling] = useState(false)
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true)



  // 检测用户是否在底部附近（距离底部50px以内认为是在底部）
  const isNearBottom = useCallback(() => {
    if (!containerRef.current) return true
    const { scrollTop, scrollHeight, clientHeight } = containerRef.current
    return scrollHeight - scrollTop - clientHeight < 50
  }, [])

  // 处理滚动事件 - 添加防抖机制
  const handleScroll = useCallback(() => {
    if (!containerRef.current) return
    
    const nearBottom = isNearBottom()
    setShouldAutoScroll(nearBottom)
    
    // 如果用户手动滚动到非底部区域，标记为用户正在滚动
    if (!nearBottom) {
      setIsUserScrolling(true)
      // 用户滚动后，延迟重置滚动状态
      setTimeout(() => {
        if (isNearBottom()) {
          setIsUserScrolling(false)
        }
      }, 1000)
    } else {
      setIsUserScrolling(false)
    }
  }, [isNearBottom])

  // 智能自动滚动 - 只有在应该自动滚动且用户没有手动滚动时才滚动
  useEffect(() => {
    if (shouldAutoScroll && !isUserScrolling) {
      // 添加延迟，避免频繁滚动
      const timeoutId = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
      }, 100)
      return () => clearTimeout(timeoutId)
    }
  }, [messages, shouldAutoScroll, isUserScrolling])

  // 当有新消息且用户在底部时，确保滚动到底部
  useEffect(() => {
    if (messages.length > 0 && isNearBottom()) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
      }, 100)
    }
  }, [messages.length, isNearBottom])



  return (
    <div 
      ref={containerRef}
      className="flex-1 px-6 py-6 pb-10 relative"
      onScroll={handleScroll}
    >
      <div className="max-w-[1500px] mx-auto space-y-6 relative">
        {messages.map((message) => (
          <div key={message.id} data-message-id={message.id} className="relative">
            {message.type === 'user' ? (
              <UserMessage content={message.content} files={message.files} />
            ) : (
              <OptimizedAssistantMessage message={message} difyApi={difyApi} />
            )}
          </div>
        ))}

        {/* 加载状态消息 - 简化显示逻辑，确保在等待AI响应时显示 */}
        {isLoading && (
          <div className="relative">
            <LoadingMessage />
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>
      
      {/* 固定目录 - 只为最新的助手消息显示，固定在页面右侧 */}
      {/* {latestAssistantMessage && latestAssistantMessage.content && (
        <TableOfContents 
          content={latestAssistantMessage.content}
          messageId={latestAssistantMessage.id}
        />
      )} */}
    </div>
  )
}, (prevProps, nextProps) => {
  // 比较isLoading状态
  if (prevProps.isLoading !== nextProps.isLoading) {
    return false;
  }

  // 只有当消息数组长度或最后一条消息发生变化时才重新渲染
  if (prevProps.messages.length !== nextProps.messages.length) {
    return false;
  }

  // 比较最后一条消息（通常是正在更新的消息）
  if (prevProps.messages.length > 0 && nextProps.messages.length > 0) {
    const prevLastMessage = prevProps.messages[prevProps.messages.length - 1];
    const nextLastMessage = nextProps.messages[nextProps.messages.length - 1];

    // 使用之前定义的消息比较函数
    return areMessagesEqual(prevLastMessage, nextLastMessage);
  }

  return true;
})

export default MessageList