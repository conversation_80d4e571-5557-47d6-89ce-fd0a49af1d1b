/**
 * 环境配置工具函数
 * 基于环境变量进行环境判断和配置获取
 */

/**
 * 支持的环境类型
 */
export type Environment = 'development' | 'test' | 'production' | 'international'

/**
 * 环境配置接口
 */
export interface EnvConfig {
  env: Environment
  apiBase: string
  defaultEmail: string
  feedbackProjectId: number
  isXAi: boolean
}

/**
 * 获取当前环境类型
 * 优先使用环境变量，如果没有则根据域名进行兼容性判断
 */
export const getCurrentEnvironment = (): Environment => {
  // 优先使用环境变量 (Vite 使用 import.meta.env)
  const envFromVar = import.meta.env.REACT_APP_ENV as Environment

  if (envFromVar && ['development', 'test', 'production', 'international'].includes(envFromVar)) {
    return envFromVar
  }

  // 兼容性处理：如果没有环境变量，根据域名判断（向后兼容）
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname

    if (hostname === 'ai.medsci.cn') {
      return 'production'
    } else if (hostname === 'ai.medon.com.cn') {
      return 'test'
    } else if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
      return 'development'
    }
  }

  // 默认返回开发环境
  return 'development'
}

/**
 * 获取环境配置
 */
export const getEnvConfig = (): EnvConfig => {
  const env = getCurrentEnvironment()

  // 从环境变量获取配置，如果没有则使用默认值 (Vite 使用 import.meta.env)
  const apiBase = import.meta.env.REACT_APP_API_BASE || getDefaultApiBase(env)
  const defaultEmail = import.meta.env.REACT_APP_DEFAULT_EMAIL || getDefaultEmail(env)
  const feedbackProjectId = Number(import.meta.env.REACT_APP_FEEDBACK_PROJECT_ID) || getDefaultFeedbackProjectId(env)

  const isXAi = import.meta.env.REACT_APP_IS_XAI === 'true'

  return {
    env,
    apiBase,
    defaultEmail,
    feedbackProjectId,
    isXAi
  }
}

/**
 * 获取默认API基础地址（兼容性处理）
 */
const getDefaultApiBase = (env: Environment): string => {
  switch (env) {
    case 'production':
    case 'test':
    case 'international':
      return '/dev-api'
    case 'development':
    default:
      return 'http://localhost:3000'
  }
}

/**
 * 获取默认邮箱（兼容性处理）
 */
const getDefaultEmail = (env: Environment): string => {
  switch (env) {
    case 'production':
      return '<EMAIL>'
    case 'international':
      return '<EMAIL>'
    case 'test':
    case 'development':
    default:
      return '<EMAIL>'
  }
}

/**
 * 获取默认反馈项目ID（兼容性处理）
 */
const getDefaultFeedbackProjectId = (env: Environment): number => {
  switch (env) {
    case 'production':
      return 250
    case 'international':
      return 300
    case 'test':
    case 'development':
    default:
      return 193
  }
}

/**
 * 判断是否为生产环境
 */
export const isProduction = (): boolean => {
  return getCurrentEnvironment() === 'production'
}

/**
 * 判断是否为开发环境
 */
export const isDevelopment = (): boolean => {
  return getCurrentEnvironment() === 'development'
}

/**
 * 判断是否为测试环境
 */
export const isTest = (): boolean => {
  return getCurrentEnvironment() === 'test'
}

/**
 * 判断是否为国际站环境
 */
export const isInternational = (): boolean => {
  return getCurrentEnvironment() === 'international'
}

/**
 * 判断是否为 XAI 环境
 */
export const isXAi = (): boolean => {
  return getEnvConfig().isXAi
}

/**
 * 获取默认语言代码
 * 国际站环境默认为英文，其他环境默认为中文
 */
export const getDefaultLanguage = (): 'zh' | 'en' => {
  return isInternational() ? 'en' : 'zh'
}

/**
 * 获取默认应用名称
 */
export const getDefaultAppName = (): string => {
  return 'novax-base'
}

/**
 * 获取默认路由路径
 * 国际站环境默认为 /en/novax-base，其他环境默认为 /zh/novax-base
 */
export const getDefaultRoute = (): string => {
  const language = getDefaultLanguage()
  const appName = getDefaultAppName()
  return `/${language}/${appName}`
}
