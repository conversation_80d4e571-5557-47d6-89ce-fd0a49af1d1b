/**
 * 对话历史记录刷新管理器
 * 负责在登录状态变化后自动刷新历史记录
 */
export class ConversationRefreshManager {
  private static refreshCallbacks: Map<string, () => void> = new Map()
  private static lastRefreshTime: number = 0
  private static refreshCooldown: number = 1000 // 1秒冷却时间
  private static pendingRefreshTimer: NodeJS.Timeout | null = null
  
  /**
   * 注册历史记录刷新回调
   */
  static registerRefreshCallback(componentId: string, callback: () => void) {
    this.refreshCallbacks.set(componentId, callback)
    console.log(`📋 ConversationRefreshManager: 注册刷新回调 ${componentId}`)
  }
  
  /**
   * 取消注册历史记录刷新回调
   */
  static unregisterRefreshCallback(componentId: string) {
    this.refreshCallbacks.delete(componentId)
    console.log(`📋 ConversationRefreshManager: 取消注册刷新回调 ${componentId}`)
  }
  
  /**
   * 触发所有组件的历史记录刷新（带防重复机制）
   */
  static triggerRefreshAll() {
    const now = Date.now()

    // 检查是否在冷却时间内
    if (now - this.lastRefreshTime < this.refreshCooldown) {
      console.log('⏰ ConversationRefreshManager: 刷新请求被冷却机制阻止，避免重复调用')
      return
    }

    // 如果有待处理的刷新，取消它
    if (this.pendingRefreshTimer) {
      clearTimeout(this.pendingRefreshTimer)
      this.pendingRefreshTimer = null
    }

    console.log('🔄 ConversationRefreshManager: 触发所有组件历史记录刷新')

    this.refreshCallbacks.forEach((callback, componentId) => {
      try {
        console.log(`🔄 ConversationRefreshManager: 刷新组件 ${componentId}`)
        callback()
      } catch (error) {
        console.error(`❌ ConversationRefreshManager: 刷新组件 ${componentId} 失败:`, error)
      }
    })

    // 更新最后刷新时间
    this.lastRefreshTime = now
  }
  
  /**
   * 延迟触发刷新，合并多个快速连续的刷新请求
   */
  static triggerRefreshDelayed(delay: number = 200) {
    // 如果有待处理的刷新，取消它
    if (this.pendingRefreshTimer) {
      clearTimeout(this.pendingRefreshTimer)
    }

    console.log(`⏱️ ConversationRefreshManager: 延迟${delay}ms触发刷新`)

    this.pendingRefreshTimer = setTimeout(() => {
      this.triggerRefreshAll()
      this.pendingRefreshTimer = null
    }, delay)
  }

  /**
   * 触发特定组件的历史记录刷新
   */
  static triggerRefresh(componentId: string) {
    const callback = this.refreshCallbacks.get(componentId)
    if (callback) {
      try {
        console.log(`🔄 ConversationRefreshManager: 刷新组件 ${componentId}`)
        callback()
      } catch (error) {
        console.error(`❌ ConversationRefreshManager: 刷新组件 ${componentId} 失败:`, error)
      }
    } else {
      console.warn(`⚠️ ConversationRefreshManager: 未找到组件 ${componentId} 的刷新回调`)
    }
  }
  
  /**
   * 初始化登录状态监听器（优化版，减少重复触发）
   */
  static initLoginStatusListener() {
    console.log('🎧 ConversationRefreshManager: 初始化登录状态监听器')

    // 只监听一个主要的登录成功事件，避免重复触发
    window.addEventListener('loginSuccess', ((event: CustomEvent) => {
      console.log('📢 ConversationRefreshManager: 收到登录成功事件')

      if (event.detail?.userInfo && event.detail?.immediate) {
        // 使用延迟刷新机制，合并可能的重复请求
        this.triggerRefreshDelayed(300)
      }
    }) as EventListener)

    // 监听登录状态变化事件（仅处理退出登录）
    window.addEventListener('loginStatusChanged', ((event: CustomEvent) => {
      console.log('📢 ConversationRefreshManager: 收到登录状态变化事件:', event.detail.type)

      if (event.detail.type === 'logout') {
        // 退出登录时清除待处理的刷新
        if (this.pendingRefreshTimer) {
          clearTimeout(this.pendingRefreshTimer)
          this.pendingRefreshTimer = null
        }
        console.log('🚪 ConversationRefreshManager: 用户已退出登录')
      }
    }) as EventListener)

    console.log('✅ ConversationRefreshManager: 登录状态监听器初始化完成')
  }
  
  /**
   * 获取当前注册的组件数量
   */
  static getRegisteredCount(): number {
    return this.refreshCallbacks.size
  }
  
  /**
   * 获取所有注册的组件ID
   */
  static getRegisteredComponents(): string[] {
    return Array.from(this.refreshCallbacks.keys())
  }
  
  /**
   * 在开发环境下提供调试工具
   */
  static initDebugTools() {
    if (process.env.NODE_ENV === 'development') {
      (window as any).ConversationRefreshManager = this
      console.log('🔧 ConversationRefreshManager调试工具已挂载到window对象')
      console.log('💡 使用方法:')
      console.log('  - window.ConversationRefreshManager.triggerRefreshAll() // 刷新所有')
      console.log('  - window.ConversationRefreshManager.getRegisteredComponents() // 查看注册组件')
    }
  }
}

// 自动初始化
ConversationRefreshManager.initLoginStatusListener()
ConversationRefreshManager.initDebugTools()

export default ConversationRefreshManager
