{"common": {"loading": "加载中...", "error": "错误", "success": "成功", "cancel": "取消", "confirm": "确认", "back": "返回", "save": "保存", "delete": "删除", "edit": "编辑", "close": "关闭", "next": "下一步", "previous": "上一步", "submit": "提交", "reset": "重置", "search": "搜索", "filter": "筛选", "sort": "排序", "refresh": "刷新", "copy": "复制", "download": "下载", "upload": "上传", "share": "分享", "settings": "设置", "help": "帮助", "about": "关于", "logout": "退出登录", "login": "登录"}, "header": {"title": "NovaX AI", "menu": "菜单", "user": "用户", "language": "语言", "version": "版本", "subscription": "订阅", "profile": "个人资料", "switchLanguage": "切换语言", "chinese": "中文", "english": "English"}, "home": {"welcome": "欢迎使用 NovaX AI", "subtitle": "您的科研灵感引擎", "description": "专为构建前瞻性研究蓝图而设计", "getStarted": "开始使用", "learnMore": "了解更多", "features": "功能特色", "services": "服务方案", "inspirationEngine": "您的灵感引擎，构建前瞻性研究蓝图", "analysisDescription": "精准剖析，深度洞察，赋能您的卓越创见"}, "chat": {"title": "对话", "newChat": "新建对话", "chatHistory": "对话历史", "inputPlaceholder": "请输入您的问题...", "send": "发送", "stop": "停止", "regenerate": "重新生成", "copy": "复制", "delete": "删除", "thinking": "思考中...", "generating": "生成中...", "searching": "搜索中...", "browsing": "浏览中...", "reviewing": "评审中...", "secondReviewing": "复审中...", "workflow": "工作流", "thinkingProcess": "思考过程", "thinkingChain": "思维链", "searchResults": "搜索结果", "browsingResults": "浏览结果", "finalAnswer": "最终回答", "clickToCollapse": "点击折叠", "clickToExpand": "点击显示", "clickToSync": "点击同步滚动到左侧对应步骤", "thinkingCollapsed": "思考过程已折叠点击显示", "browsingCollapsed": "浏览结果已折叠点击显示", "reviewCollapsed": "评审过程已折叠点击显示", "secondReviewCollapsed": "复审过程已折叠点击显示", "nodeDataCollapsed": "节点数据已折叠点击显示", "characters": "字符", "steps": "步骤", "sources": "来源", "nodeLoading": "节点加载", "expandDetails": "展开详情", "collapseDetails": "折叠详情", "loading": "正在加载中...", "completed": "已完成回答", "preparing": "准备", "executionFailed": "执行失败", "reviewingFinalResult": "正在评审最终结果", "secondReviewingFinalResult": "正在复审最终结果", "workflowRunning": "工作流运行中", "workflowFinished": "工作流已完成", "workflowError": "工作流错误", "uploadFile": "上传文件", "fileUploaded": "文件已上传", "fileUploadError": "文件上传失败", "maxFilesReached": "已达到最大文件数量限制 ({count} 个)", "maxFilesExceeded": "最多只能再选择 {available} 个文件，总数不能超过 {max} 个", "batchUploadLimit": "每次最多只能选择 2 个文件", "unsupportedFileType": "文件 \"{fileName}\" 格式不支持，支持的格式：{formats}", "fileSizeExceeded": "文件 \"{fileName}\" 超过大小限制 ({maxSize}MB)", "fileUploadNotSupported": "当前应用不支持文件上传", "appNotInitialized": "应用未初始化，请稍后重试", "uploadFailed": "文件上传失败", "networkError": "网络连接错误", "uploadTimeout": "文件上传超时", "fileSizeLimit": "文件大小超出限制", "uploadSuccess": "文件上传成功", "noAuthTokenWarning": "未找到认证令牌", "noAuthToken": "认证令牌缺失", "initializationFailed": "初始化失败", "authFailed": "认证失败", "refreshInitFailed": "刷新初始化失败", "unknownFile": "未知文件", "historyResponseFormatError": "历史记录响应格式错误", "historyLoadFailed": "历史记录加载失败", "loadHistoryFailed": "加载历史记录失败", "sendMessageFailed": "发送消息失败", "stopTask": "停止任务", "initializing": "初始化中...", "reload": "重新加载", "loadingHistory": "加载历史记录中...", "retry": "重试"}, "sidebar": {"conversations": "对话列表", "newConversation": "新建对话", "searchConversations": "搜索对话", "noConversations": "暂无对话", "deleteConversation": "删除对话", "renameConversation": "重命名对话", "conversationDeleted": "对话已删除", "conversationRenamed": "对话已重命名"}, "apps": {"novaxBase": {"name": "NovaX Base", "description": "核心思路启发", "subtitle": "基于研究背景进行单次科研方向思路设计"}, "novaxPro": {"name": "NovaX Pro", "description": "个性化研究规划", "subtitle": "基于当前研究基础与可用条件进行深度匹配"}, "novaxUltra": {"name": "NovaX Ultra", "description": "构筑创新生态", "subtitle": "战略性课题集群，超越单一思路"}}, "services": {"title": "服务方案体系", "base": {"title": "NovaX Base - 核心思路启发", "description": "基于研究背景进行单次科研方向思路设计，获取创新思路的初步构想，点亮您的研究起点。"}, "pro": {"title": "NovaX Pro - 个性化研究规划", "description": "基于当前研究基础与可用条件进行深度匹配，提供个性化的研究设计方案与合理的科研实施规划。"}, "ultra": {"title": "NovaX Ultra - 构筑创新生态", "description": "战略性课题集群，超越单一思路，智能规划多点联动、可持续发展的系列创新研究课题群。"}, "advantages": {"title": "核心优势", "aiDriven": "AI驱动：基于先进的人工智能技术", "personalMatch": "个性匹配：深度理解用户研究背景", "forwardInsight": "前瞻洞察：把握未来发展趋势"}}, "subscription": {"title": "订阅管理", "currentPlan": "当前套餐", "upgrade": "升级", "renew": "续费", "expired": "已过期", "active": "有效", "inactive": "未激活", "subscribe": "订阅", "unsubscribe": "取消订阅", "medsciAccount": "梅斯账号"}, "errors": {"networkError": "网络连接错误", "serverError": "服务器错误", "unauthorized": "未授权访问", "forbidden": "访问被禁止", "notFound": "页面未找到", "validationError": "输入验证失败", "uploadError": "上传失败", "downloadError": "下载失败", "loginRequired": "请先登录", "subscriptionRequired": "需要订阅服务", "insufficientPermissions": "权限不足"}, "messages": {"saveSuccess": "保存成功", "deleteSuccess": "删除成功", "updateSuccess": "更新成功", "operationSuccess": "操作成功", "operationFailed": "操作失败", "confirmDelete": "确认删除此项目？", "unsavedChanges": "有未保存的更改，确认离开？", "processingRequest": "正在处理请求...", "requestCompleted": "请求已完成", "requestFailed": "请求失败"}, "workflow": {"nodes": {"start": "开始节点", "ifElse": "条件判断", "documentExtractor": "文档提取", "tool": "工具调用", "llm": "AI分析", "answer": "生成回答", "assigner": "变量赋值", "variableAggregator": "数据聚合", "questionClassifier": "问题分类", "code": "代码执行", "templateTransform": "模板转换", "httpRequest": "HTTP请求", "parameterExtractor": "参数提取"}, "status": {"running": "运行中", "success": "成功", "error": "错误", "finished": "已完成", "preparing": "准备中"}}, "fileUpload": {"title": "文件上传", "dragAndDrop": "拖拽文件到此处或点击上传", "dropToUpload": "释放文件以上传", "selectFiles": "选择文件", "supportedFormats": "支持的格式", "maxFileSize": "最大文件大小", "maxFiles": "最多上传: {count} 个文件", "multiFileSupport": "支持多文件同时上传", "uploading": "上传中...", "uploadSuccess": "上传成功", "uploadFailed": "上传失败", "removeFile": "移除文件", "fileName": "文件名", "fileSize": "文件大小", "fileType": "文件类型"}, "auth": {"loginRequired": "请先登录", "loginButton": "登录", "logoutButton": "退出登录", "userProfile": "用户资料", "loggedIn": "已登录", "notLoggedIn": "未登录", "loginSuccess": "登录成功", "logoutSuccess": "退出成功"}, "navigation": {"home": "首页", "chat": "对话", "history": "历史记录", "settings": "设置", "about": "关于", "feedback": "意见反馈", "export": "导出数据", "import": "导入数据"}, "feedback": {"titleLabel": "反馈标题", "titlePlaceholder": "请输入反馈标题", "titleRequired": "请输入反馈标题", "titleMaxLength": "标题不能超过100个字符", "contentLabel": "反馈内容", "contentPlaceholder": "请详细描述您遇到的问题或建议，我们会认真对待每一条反馈", "contentRequired": "请输入反馈内容", "contentMinLength": "反馈内容至少需要10个字符", "contentMaxLength": "反馈内容不能超过1000个字符", "submit": "提交反馈", "submitSuccess": "反馈提交成功，感谢您的宝贵意见！", "submitError": "提交失败，请稍后重试", "loginRequired": "请先登录后再提交反馈"}, "cases": {"title": "案例示例", "description": "查看使用案例和示例", "viewCase": "查看案例", "tryExample": "试试这个例子", "moreExamples": "更多示例", "noCases": "暂无相关案例"}, "payment": {"title": "支付订阅", "currentPlan": "当前套餐", "choosePlan": "选择套餐", "payNow": "立即支付", "paymentSuccess": "支付成功", "paymentFailed": "支付失败", "subscriptionExpired": "订阅已过期", "renewSubscription": "续费订阅", "cancelSubscription": "取消订阅", "upgradeSubscription": "升级订阅"}, "mobile": {"menu": "菜单", "close": "关闭", "openSidebar": "打开侧边栏", "closeSidebar": "关闭侧边栏"}, "pageTitle": {"separator": " - ", "suffix": "AI智能助手", "home": "首页", "chat": "对话", "newChat": "新建对话", "loading": "加载中", "error": "错误", "notFound": "页面未找到", "apps": {"novax-base": "NovaX Base", "novax-pro": "NovaX Pro", "novax-ultra": "NovaX Ultra", "elavax-base": "ElaVaX Base", "elavax-pro": "ElaVaX Pro", "elavax-ultra": "ElaVaX Ultra"}, "templates": {"home": "{appName}{separator}{suffix}", "chat": "{chatTitle}{separator}{appName}{separator}{suffix}", "newChat": "{newChat}{separator}{appName}{separator}{suffix}", "loading": "{loading}{separator}{appName}{separator}{suffix}", "error": "{error}{separator}{appName}{separator}{suffix}", "notFound": "{notFound}{separator}{suffix}"}}}