import { useState, useCallback, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  t as translate,
  getCurrentLanguage,
  setLanguage,
  initLanguage,
  addLanguageChangeListener,
  removeLanguageChangeListener,
  type SupportedLanguage
} from './simple'
import { getDefaultAppName } from '../utils/envConfig'

/**
 * 从路径中提取语言代码
 */
const extractLanguageFromPath = (pathname: string): SupportedLanguage => {
  const segments = pathname.split('/').filter(Boolean)
  const firstSegment = segments[0]

  if (firstSegment && (firstSegment === 'zh' || firstSegment === 'en')) {
    return firstSegment as SupportedLanguage
  }

  return 'zh' // 默认中文
}

/**
 * 构建新的路径，替换语言部分
 */
const buildNewPath = (currentPath: string, newLang: SupportedLanguage): string => {
  const segments = currentPath.split('/').filter(Boolean)

  // 如果第一个段是语言代码，替换它
  if (segments.length > 0 && (segments[0] === 'zh' || segments[0] === 'en')) {
    segments[0] = newLang
    return '/' + segments.join('/')
  }

  // 如果没有语言代码，添加到开头
  if (segments.length > 0) {
    return '/' + newLang + '/' + segments.join('/')
  }

  // 如果是根路径，返回语言首页
  return `/${newLang}/${getDefaultAppName()}`
}

/**
 * 简化的翻译Hook，集成路由功能
 */
export const useSimpleTranslation = () => {
  const navigate = useNavigate()
  const location = useLocation()

  // 从URL中获取当前语言，这是真实的语言状态
  const urlLanguage = extractLanguageFromPath(location.pathname)
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(urlLanguage)

  // 当URL路径变化时，立即更新语言状态
  useEffect(() => {
    const urlLang = extractLanguageFromPath(location.pathname)
    console.log('URL changed, detected language:', urlLang, 'from path:', location.pathname)

    // 同步URL语言到全局状态和本地状态
    setLanguage(urlLang)
    setCurrentLanguage(urlLang)
  }, [location.pathname])

  // 监听全局语言变化（用于其他组件触发的变化）
  useEffect(() => {
    const handleLanguageChange = (newLang: SupportedLanguage) => {
      console.log('Global language changed to:', newLang)
      setCurrentLanguage(newLang)
    }

    addLanguageChangeListener(handleLanguageChange)

    return () => {
      removeLanguageChangeListener(handleLanguageChange)
    }
  }, [])

  // 翻译函数，直接使用当前语言状态
  const t = useCallback((key: string): string => {
    const result = translate(key)
    // console.log(`Translating "${key}" to "${result}" in language "${currentLanguage}"`)
    return result
  }, [currentLanguage]) // 只依赖currentLanguage

  // 语言切换函数，同时更新URL和状态
  const changeLanguage = useCallback((lang: SupportedLanguage) => {
    if (lang === currentLanguage) return

    console.log('Changing language from', currentLanguage, 'to', lang)

    // 构建新的URL路径
    const newPath = buildNewPath(location.pathname, lang)
    console.log('Navigating to new path:', newPath)

    // 导航到新路径（这会触发useEffect中的路径变化处理）
    navigate(newPath, { replace: true })
  }, [currentLanguage, location.pathname, navigate])

  return {
    t,
    currentLanguage,
    changeLanguage
  }
}

/**
 * 路由感知的国际化Hook
 */
export const useI18nRouter = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { currentLanguage, changeLanguage } = useSimpleTranslation()

  // 从当前路径解析信息
  const segments = location.pathname.split('/').filter(Boolean)
  const appName = segments.length >= 2 ? segments[1] : 'novax-base'
  const sessionId = segments.length >= 3 ? segments[2] : null

  // 导航到指定应用
  const navigateToApp = useCallback((appName: string, sessionId?: string, lang?: SupportedLanguage) => {
    const targetLang = lang || currentLanguage
    const path = sessionId ? `/${targetLang}/${appName}/${sessionId}` : `/${targetLang}/${appName}`
    navigate(path)
  }, [currentLanguage, navigate])

  // 导航到首页
  const navigateToHome = useCallback((appName: string = 'novax-base', lang?: SupportedLanguage) => {
    const targetLang = lang || currentLanguage
    navigate(`/${targetLang}/${appName}`)
  }, [currentLanguage, navigate])

  return {
    currentLanguage,
    currentAppName: appName,
    currentSessionId: sessionId,
    changeLanguage,
    navigateToApp,
    navigateToHome,
    pathname: location.pathname
  }
}
