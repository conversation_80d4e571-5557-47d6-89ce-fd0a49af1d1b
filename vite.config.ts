import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');

  return {
    // 根据环境设置base路径
    // 国际化环境使用绝对路径，避免静态资源路径包含语言前缀
    base: env.REACT_APP_ENV === 'international' ? '/' : './',
    plugins: [react()],
    optimizeDeps: {
      exclude: ["bippy/dist/jsx-dev-runtime", "bippy/dist/jsx-runtime"],
    },
    // 定义全局常量，确保环境变量在构建时被正确替换
    define: {
      'import.meta.env.REACT_APP_ENV': JSON.stringify(env.REACT_APP_ENV),
      'import.meta.env.REACT_APP_API_BASE': JSON.stringify(env.REACT_APP_API_BASE),
      'import.meta.env.REACT_APP_DEFAULT_EMAIL': JSON.stringify(env.REACT_APP_DEFAULT_EMAIL),
      'import.meta.env.REACT_APP_FEEDBACK_PROJECT_ID': JSON.stringify(env.REACT_APP_FEEDBACK_PROJECT_ID),
      'import.meta.env.REACT_APP_IS_XAI': JSON.stringify(env.REACT_APP_IS_XAI),
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      rollupOptions: {
        output: {
          // 确保资源文件名包含hash，便于缓存
          assetFileNames: 'assets/[name]-[hash][extname]',
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js'
        }
      }
    },
    server: {
      port: 3000, // 设置你想要的端口号
      proxy: {
        '/ai-base': {
          target: 'http://192.168.16.243:48081/',
          changeOrigin: true,
        }
      }
    },
  }
});
