# ============================================================================
# Nginx Configuration for International MedXY.ai Site
# ============================================================================
# 
# 项目：国际化React/Vite SPA应用
# 域名：medxy.ai / www.medxy.ai
# 默认路由：/en/novax-base
# 
# 功能特性：
# - 支持HTTPS和HTTP自动重定向
# - SPA路由支持（解决刷新404问题）
# - 国际化路由结构支持
# - API代理配置
# - 静态资源优化
# - 安全头配置
# - 性能优化
# ============================================================================

# HTTP服务器 - 重定向到HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name medxy.ai www.medxy.ai;
    
    # 安全重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

# 主HTTPS服务器配置
server {
    # ========================================================================
    # 基础服务器配置
    # ========================================================================
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name medxy.ai www.medxy.ai;
    
    # 根目录设置
    root /usr/share/nginx/html;
    index index.html;
    
    # ========================================================================
    # SSL/TLS配置
    # ========================================================================
    ssl_certificate /etc/nginx/ssl/medxy.ai.crt;
    ssl_certificate_key /etc/nginx/ssl/medxy.ai.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # ========================================================================
    # 客户端配置
    # ========================================================================
    client_max_body_size 256m;
    client_header_buffer_size 10240k;
    large_client_header_buffers 6 10240k;
    
    # ========================================================================
    # 代理超时配置
    # ========================================================================
    proxy_connect_timeout 300s;
    proxy_read_timeout 300s;
    proxy_send_timeout 300s;
    
    # ========================================================================
    # 安全头配置
    # ========================================================================
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://ai.medsci.cn;" always;
    
    # ========================================================================
    # Gzip压缩配置
    # ========================================================================
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # ========================================================================
    # API代理配置
    # ========================================================================
    
    # 代理所有API请求到主站
    location /dev-api/ {
        # 代理到主站API
        proxy_pass https://ai.medsci.cn/dev-api/;
        
        # 代理头设置
        proxy_set_header Host ai.medsci.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Original-Host $host;
        
        # CORS处理
        proxy_set_header Origin https://ai.medsci.cn;
        
        # 超时设置
        proxy_connect_timeout 300s;
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
        
        # 流式响应优化
        proxy_buffering off;
        proxy_cache off;
        proxy_http_version 1.1;
        
        # 禁用缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # ========================================================================
    # 静态资源配置（高优先级，使用^~前缀匹配）
    # ========================================================================

    # assets目录静态资源（最高优先级）
    location ^~ /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";

        # 尝试直接提供文件，如果不存在则返回404
        try_files $uri =404;
    }

    # static目录静态资源
    location ^~ /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";

        try_files $uri =404;
    }

    # 字体文件缓存（通过文件扩展名匹配）
    location ~* \.(woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }

    # 图片资源缓存
    location ~* \.(jpg|jpeg|png|gif|ico|svg)$ {
        expires 1M;
        add_header Cache-Control "public";
    }

    # CSS和JS文件缓存（除了assets目录，因为assets已经被上面的规则处理）
    location ~* ^/(?!assets/).*\.(css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 根目录常见静态文件
    location ~* ^/(favicon\.ico|robots\.txt|sitemap\.xml|manifest\.json)$ {
        expires 1M;
        add_header Cache-Control "public";
        try_files $uri =404;
    }

    # 其他静态资源
    location ~* \.(pdf|doc|docx|xls|xlsx|ppt|pptx)$ {
        expires 1M;
        add_header Cache-Control "public";
    }
    
    # ========================================================================
    # SPA路由支持配置
    # ========================================================================

    # 国际化路由支持 - 英文版应用
    # 支持路径：/en/{app-name}, /en/{app-name}/new, /en/{app-name}/{session-id}
    location ~ ^/en/(novax-base|novax-pro|novax-ultra|elavax-base|elavax-pro|elavax-ultra)(/.*)?$ {
        try_files $uri $uri/ /index.html;

        # 禁用缓存HTML文件
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # 国际化路由支持 - 中文版应用（向后兼容）
    # 支持路径：/zh/{app-name}, /zh/{app-name}/new, /zh/{app-name}/{session-id}
    location ~ ^/zh/(novax-base|novax-pro|novax-ultra|elavax-base|elavax-pro|elavax-ultra)(/.*)?$ {
        try_files $uri $uri/ /index.html;

        # 禁用缓存HTML文件
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # 案例页面路由支持
    # 支持路径：/{lang}/cases/{case-id}
    location ~ ^/(en|zh)/cases/[^/]+(/.*)?$ {
        try_files $uri $uri/ /index.html;

        # 禁用缓存HTML文件
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # ========================================================================
    # 根路径和默认路由
    # ========================================================================
    
    # 根路径处理 - 重定向到默认国际化路由
    location = / {
        return 302 /en/novax-base;
    }

    # 处理不带语言前缀的应用路径（仅限已知应用名）
    location ~ ^/(novax-base|novax-pro|novax-ultra|elavax-base|elavax-pro|elavax-ultra)(/.*)?$ {
        return 302 /en$request_uri;
    }
    
    # ========================================================================
    # 主HTML文件配置
    # ========================================================================
    
    # 主应用HTML文件
    location = /index.html {
        # 禁用缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";
    }
    
    # ========================================================================
    # 健康检查和监控
    # ========================================================================
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Nginx状态监控（仅内网访问）
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow 172.16.0.0/12;
        allow 192.168.0.0/16;
        deny all;
    }
    
    # ========================================================================
    # 通用fallback规则
    # ========================================================================

    # 所有其他路径都fallback到index.html（由前端路由处理）
    location / {
        try_files $uri $uri/ /index.html;

        # 禁用缓存HTML文件
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # ========================================================================
    # 错误页面配置
    # ========================================================================

    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
    
    # ========================================================================
    # 日志配置
    # ========================================================================
    
    # 访问日志
    access_log /var/log/nginx/medxy.ai.access.log combined;
    
    # 错误日志
    error_log /var/log/nginx/medxy.ai.error.log warn;
}

# ============================================================================
# 额外配置说明
# ============================================================================
#
# 1. SSL证书配置：
#    - 请将SSL证书文件放置在 /etc/nginx/ssl/ 目录
#    - 证书文件：medxy.ai.crt
#    - 私钥文件：medxy.ai.key
#
# 2. 日志文件：
#    - 访问日志：/var/log/nginx/medxy.ai.access.log
#    - 错误日志：/var/log/nginx/medxy.ai.error.log
#
# 3. 性能优化：
#    - 启用了HTTP/2
#    - 配置了Gzip压缩
#    - 设置了合适的缓存策略
#
# 4. 安全配置：
#    - 强制HTTPS
#    - 配置了安全头
#    - 设置了CSP策略
#
# 5. SPA路由支持：
#    - 所有前端路由都会fallback到index.html
#    - 支持国际化路由结构
#    - 根路径自动重定向到/en/novax-base
#
# ============================================================================
